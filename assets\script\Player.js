cc.Class({
    extends: cc.Component,

    properties: {
        // 跳跃力度
        jumpForce: 1500,

        // 最大下落高度，超过此高度视为游戏失败
        maxFallHeight: 500,

        // 最大跳跃次数
        maxJumpTimes: 5,

        // 玩家在X轴的固定位置
        fixedPositionX: -200,

        // 滑行持续时间
        slideDuration: 0.01,

        // 滑行时的碰撞体高度缩放
        slideColliderScale: 0.5,



        // 跳跃动画持续时间（秒）
        jumpAnimationDuration: 0.5,

        // 射线检测距离
        raycastDistance: 15
    },

    onLoad() {
        // 初始化状态
        this.isGameOver = false;
        this.jumpCount = 0;
        this.isOnGround = false;
        this.isSliding = false;

        // 获取刚体组件
        this.rigidBody = this.getComponent(cc.RigidBody);

        // 获取动画组件
        this.animationComponent = this.getComponent(cc.Animation);

        // 获取碰撞体组件 - 只获取PhysicsBoxCollider
        this.collider = this.getComponent(cc.PhysicsBoxCollider);

        if (this.collider) {
            // PhysicsBoxCollider使用size属性
            this.originalColliderSize = this.collider.size.clone();
            console.log("找到PhysicsBoxCollider，尺寸:", this.collider.size);
        } else {
            console.error("未找到PhysicsBoxCollider组件！");
            this.originalColliderSize = null;
        }



        // 记录初始位置（用于重置）
        this.initialPosition = cc.v2(this.node.x, this.node.y);

        // 射线检测相关
        this.groundCheckDelay = 0;
        this.lastGroundContactTime = 0;

        // 设置动画事件监听
        this.setupAnimationEvents();

        // 绑定碰撞事件
        this.node.on('onBeginContact', this.onBeginContact, this);
      
    },

    start() {
        // 固定X轴位置
        this.node.x = this.fixedPositionX;
    },

    update(dt) {
        // 保持X轴位置不变
        this.node.x = this.fixedPositionX;
        // 检查是否超过最大下落高度
        if (!this.isGameOver && this.initialPosition.y - this.node.y > this.maxFallHeight) {
            this.gameOver();
        }
    },



    setupAnimationEvents() {
        // 使用定时器方式来处理动画切换，更可靠
        console.log("动画事件设置完成");
    },

    playDefaultAnimation() {
        if (this.animationComponent) {
            this.animationComponent.play('playeridle');
            console.log("播放默认动画: playeridle");
        }
    },



    jump() {
        if (this.isGameOver || this.isSliding) {
            return;
        }

        // 跳跃条件：在地面上或者还有剩余跳跃次数
        if (this.isOnGround || this.jumpCount < this.maxJumpTimes) {
            // 直接设置速度
            this.rigidBody.linearVelocity = cc.v2(0, this.jumpForce);

            // 增加跳跃计数
            this.jumpCount++;

            // 标记不在地面上
            this.isOnGround = false;

            // 播放跳跃动画
            if (this.animationComponent) {
                this.animationComponent.play('playerjump');

                // 设置定时器切换回默认动画
                this.scheduleOnce(() => {
                    this.playDefaultAnimation();
                }, this.jumpAnimationDuration);

                console.log("跳跃动画播放，", this.jumpAnimationDuration, "秒后切换回默认动画");
            }

            console.log("玩家执行跳跃，当前跳跃次数:", this.jumpCount);


        }
    },

    slide() {
        if (this.isGameOver || this.isSliding || !this.isOnGround) {
            return;
        }

        console.log("玩家执行滑行");

        // 设置滑行状态
        this.isSliding = true;

        // 播放滑行动画
        if (this.animationComponent) {
            this.animationComponent.play('playerslide');
        }

        // 缩小碰撞体（模拟蹲下）
        if (this.collider && this.originalColliderSize) {
            this.collider.size = cc.size(
                this.originalColliderSize.width,
                this.originalColliderSize.height * this.slideColliderScale
            );
            this.collider.apply();
        }

        // 设置滑行结束定时器（使用滑行持续时间）
        this.scheduleOnce(() => {
            this.endSlide();
        }, this.slideDuration);


    },

    endSlide() {
        if (!this.isSliding) {
            return;
        }

        console.log("玩家滑行结束");

        // 重置滑行状态
        this.isSliding = false;

        // 恢复碰撞体大小
        if (this.collider && this.originalColliderSize) {
            this.collider.size = this.originalColliderSize.clone();
            this.collider.apply();
        }

        // 播放默认动画
        this.playDefaultAnimation();
    },

    onBeginContact(contact, selfCollider, otherCollider) {
        let isPlatform = otherCollider.node.group === 'Ground';

        if (isPlatform) {
            // 记录接触时间
            this.lastGroundContactTime = Date.now();

            // 简化地面检测：只要碰到地面且玩家向下运动或静止，就认为落地
            if (this.rigidBody.linearVelocity.y <= 100) {
                this.isOnGround = true;
                this.jumpCount = 0; // 重置跳跃次数
                console.log("碰撞检测：角色落地");
            }
        }
    },



    gameOver() {
        this.isGameOver = true;
        console.log("游戏结束");

        // 可以在这里添加游戏结束逻辑
        // cc.director.pause(); // 游戏暂停
    },

    reset() {
        // 重置位置
        this.node.x = this.initialPosition.x;
        this.node.y = this.initialPosition.y;

        // 重置状态
        this.isGameOver = false;
        this.jumpCount = 0;
        this.isOnGround = false;
        this.isSliding = false;
        this.groundCheckDelay = 0;
        this.lastGroundContactTime = 0;

        // 重置速度
        if (this.rigidBody) {
            this.rigidBody.linearVelocity = cc.v2(0, 0);
        }

        // 重置碰撞体
        if (this.collider && this.originalColliderSize) {
            this.collider.size = this.originalColliderSize.clone();
            this.collider.apply();
        }

        // 取消所有定时器
        this.unscheduleAllCallbacks();
    },

    onDestroy() {
        // 移除碰撞事件监听
        this.node.off('onBeginContact', this.onBeginContact, this);
        

        // 取消所有定时器
        this.unscheduleAllCallbacks();
    }
});