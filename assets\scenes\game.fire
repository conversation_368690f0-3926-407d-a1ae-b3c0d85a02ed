[{"__type__": "cc.SceneAsset", "_name": "", "_objFlags": 0, "_native": "", "scene": {"__id__": 1}}, {"__type__": "cc.Scene", "_objFlags": 0, "_parent": null, "_children": [{"__id__": 2}], "_active": false, "_components": [], "_prefab": null, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 0, "height": 0}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_is3DNode": true, "_groupIndex": 0, "groupIndex": 0, "autoReleaseAssets": false, "_id": "1de4b909-6f03-463d-a946-c5dbc56ae34e"}, {"__type__": "cc.Node", "_name": "<PERSON><PERSON>", "_objFlags": 0, "_parent": {"__id__": 1}, "_children": [{"__id__": 3}, {"__id__": 7}, {"__id__": 9}, {"__id__": 11}, {"__id__": 13}, {"__id__": 17}, {"__id__": 22}, {"__id__": 27}], "_active": true, "_components": [{"__id__": 36}, {"__id__": 37}, {"__id__": 38}, {"__id__": 39}], "_prefab": null, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 1111, "height": 500}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [555.5, 250, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "b3geUhAP9OYYM3iQYxi0To"}, {"__type__": "cc.Node", "_name": "Background<PERSON><PERSON><PERSON>", "_objFlags": 0, "_parent": {"__id__": 2}, "_children": [{"__id__": 4}], "_active": true, "_components": [{"__id__": 6}], "_prefab": null, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 0, "height": 0}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "6brjxgbjpOOYJW4jGlmi5C"}, {"__type__": "cc.Node", "_name": "Map", "_objFlags": 0, "_parent": {"__id__": 3}, "_children": [], "_active": false, "_components": [{"__id__": 5}], "_prefab": null, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 1440, "height": 500}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "ee5WA+O7lNgo6Hfbv6tjF5"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 4}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "e8e04552-3429-426a-9f5b-e878fb65a2f3"}, "_type": 0, "_sizeMode": 0, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_atlas": null, "_id": "6aj4sovVBPSpc8hHSY7Knk"}, {"__type__": "357c70xDrpCapCAfouR8w06", "_name": "", "_objFlags": 0, "node": {"__id__": 3}, "_enabled": true, "backgroundSprites": [{"__uuid__": "e8e04552-3429-426a-9f5b-e878fb65a2f3"}], "scrollSpeed": 100, "changeInterval": 10, "enableRandomChange": true, "overlapPixels": 20, "_id": "73qJzHh2JEspaQWd31Y+AB"}, {"__type__": "cc.Node", "_name": "Main Camera", "_objFlags": 0, "_parent": {"__id__": 2}, "_children": [], "_active": true, "_components": [{"__id__": 8}], "_prefab": null, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 0, "height": 0}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "08RkNyvWtFRIVsqE6WnpZ2"}, {"__type__": "cc.Camera", "_name": "", "_objFlags": 0, "node": {"__id__": 7}, "_enabled": true, "_cullingMask": 4294967295, "_clearFlags": 7, "_backgroundColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_depth": -1, "_zoomRatio": 1, "_targetTexture": null, "_fov": 60, "_orthoSize": 10, "_nearClip": 1, "_farClip": 4096, "_ortho": true, "_rect": {"__type__": "cc.Rect", "x": 0, "y": 0, "width": 1, "height": 1}, "_renderStages": 1, "_alignWithScreen": true, "_id": "99wysfwyFGKIM2f4zLp0V0"}, {"__type__": "cc.Node", "_name": "PlatformManager", "_objFlags": 0, "_parent": {"__id__": 2}, "_children": [], "_active": true, "_components": [{"__id__": 10}], "_prefab": null, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 0, "height": 0}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "ffRY93HjhHd75JkQyDDVfB"}, {"__type__": "ebebfkawXJCzb2TIlmDcsRT", "_name": "", "_objFlags": 0, "node": {"__id__": 9}, "_enabled": true, "moveSpeed": 300.4, "minGapX": 150, "maxGapX": 220, "platformPrefabs": [{"__uuid__": "47d89913-0707-4684-8f1e-706b69abdc11"}, {"__uuid__": "5c798564-78e3-40ac-abec-aff1a0063ef2"}, {"__uuid__": "d52bb9a2-7ac6-46ab-a85f-e7d799babc62"}, {"__uuid__": "9ac40c90-1cb1-49c6-9439-d9d608481d9e"}, {"__uuid__": "b0f058d5-1025-4415-9ec4-ddcd601ab457"}], "preGenerateDistance": 1, "_id": "78AFjEiXdPpIsDoFMKSypn"}, {"__type__": "cc.Node", "_name": "coinMgr", "_objFlags": 0, "_parent": {"__id__": 2}, "_children": [], "_active": true, "_components": [{"__id__": 12}], "_prefab": null, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 0, "height": 0}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "08JvQ69NZHGa2ZseGUkZQZ"}, {"__type__": "168bbq9VuRBd7DpZDmH5vuy", "_name": "", "_objFlags": 0, "node": {"__id__": 11}, "_enabled": true, "coinprefab": {"__uuid__": "b89bd62a-e40d-41e8-9d54-58c8afb2e2cd"}, "spawnInterval": 2, "coinsPerGroup": 8, "coinSpacing": 60, "groupPattern": 4, "_id": "f6P5UGh7FBqIfgvlHTYw15"}, {"__type__": "cc.Node", "_name": "ScoreManager", "_objFlags": 0, "_parent": {"__id__": 2}, "_children": [{"__id__": 14}], "_active": true, "_components": [{"__id__": 16}], "_prefab": null, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 0, "height": 0}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "cfMfJCUhFLAJGcq/wy/EiG"}, {"__type__": "cc.Node", "_name": "label", "_objFlags": 0, "_parent": {"__id__": 13}, "_children": [], "_active": true, "_components": [{"__id__": 15}], "_prefab": null, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 97.87, "height": 50.4}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-197.169, 186.929, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "57rrKrJgJO3IWlU6jgka3w"}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "node": {"__id__": 14}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_string": "Label", "_N$string": "Label", "_fontSize": 40, "_lineHeight": 40, "_enableWrapText": true, "_N$file": null, "_isSystemFontUsed": true, "_spacingX": 0, "_batchAsBitmap": false, "_styleFlags": 0, "_underlineHeight": 0, "_N$horizontalAlign": 1, "_N$verticalAlign": 1, "_N$fontFamily": "<PERSON><PERSON>", "_N$overflow": 0, "_N$cacheMode": 0, "_id": "22071SKkdKzqiW08X/E9Su"}, {"__type__": "2357azX8kNEPIgullD9yxw5", "_name": "", "_objFlags": 0, "node": {"__id__": 13}, "_enabled": true, "scoreLabel": {"__id__": 15}, "coinValue": 10, "comboBonus": true, "comboTimeWindow": 2, "_id": "21GUY/+WtIPbhgRjtglgbU"}, {"__type__": "cc.Node", "_name": "jump", "_objFlags": 512, "_parent": {"__id__": 2}, "_children": [{"__id__": 18}], "_active": true, "_components": [{"__id__": 20}, {"__id__": 21}], "_prefab": null, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 156, "height": 154}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-384.229, -131, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "26j0wsWJNAe58NSAnJeqgY"}, {"__type__": "cc.Node", "_name": "jumpchild", "_objFlags": 0, "_parent": {"__id__": 17}, "_children": [], "_active": true, "_components": [{"__id__": 19}], "_prefab": null, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 83, "height": 97}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-4, 4, 0, 0, 0, 0, 1, 1, 1, 0]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "adcMVh/wpF6IvlGGviCsUG"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 18}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "fe70170f-860a-44df-b09e-6bb60eee88f4"}, "_type": 0, "_sizeMode": 1, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_atlas": null, "_id": "19mfzgUnhDhq4VVoHcUnsk"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 17}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "bca84976-f7e4-48b9-8af5-3a8fe66814f2"}, "_type": 0, "_sizeMode": 1, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_atlas": null, "_id": "73fUHx2h9Id4/G3ZaEu9Ff"}, {"__type__": "cc.<PERSON><PERSON>", "_name": "", "_objFlags": 0, "node": {"__id__": 17}, "_enabled": true, "_normalMaterial": null, "_grayMaterial": null, "duration": 0.1, "zoomScale": 1.2, "clickEvents": [], "_N$interactable": true, "_N$enableAutoGrayEffect": false, "_N$transition": 3, "transition": 3, "_N$normalColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_N$pressedColor": {"__type__": "cc.Color", "r": 211, "g": 211, "b": 211, "a": 255}, "pressedColor": {"__type__": "cc.Color", "r": 211, "g": 211, "b": 211, "a": 255}, "_N$hoverColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "hoverColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_N$disabledColor": {"__type__": "cc.Color", "r": 124, "g": 124, "b": 124, "a": 255}, "_N$normalSprite": null, "_N$pressedSprite": null, "pressedSprite": null, "_N$hoverSprite": null, "hoverSprite": null, "_N$disabledSprite": null, "_N$target": {"__id__": 18}, "_id": "1ekr2e8glDCYuP1qEDyDqH"}, {"__type__": "cc.Node", "_name": "slide", "_objFlags": 512, "_parent": {"__id__": 2}, "_children": [{"__id__": 23}], "_active": true, "_components": [{"__id__": 25}, {"__id__": 26}], "_prefab": null, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 156, "height": 154}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [382.404, -131, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "17c6nhF+pHOpEqqG99TebJ"}, {"__type__": "cc.Node", "_name": "slidechild", "_objFlags": 0, "_parent": {"__id__": 22}, "_children": [], "_active": true, "_components": [{"__id__": 24}], "_prefab": null, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 133, "height": 80}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-5.036, 4, 0, 0, 0, 0, 1, 1, 1, 0]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "4cjQxuBAxDYI0L98zXH/f1"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 23}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "cc5d082b-3bc5-4c97-8a58-6794a5e5b31a"}, "_type": 0, "_sizeMode": 1, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_atlas": null, "_id": "dbCcretqRKdIxweMxHfa8g"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 22}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "bca84976-f7e4-48b9-8af5-3a8fe66814f2"}, "_type": 0, "_sizeMode": 1, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_atlas": null, "_id": "00NBqD+ShMAITGtkG/v7fh"}, {"__type__": "cc.<PERSON><PERSON>", "_name": "", "_objFlags": 0, "node": {"__id__": 22}, "_enabled": true, "_normalMaterial": null, "_grayMaterial": null, "duration": 0.1, "zoomScale": 1.2, "clickEvents": [], "_N$interactable": true, "_N$enableAutoGrayEffect": false, "_N$transition": 3, "transition": 3, "_N$normalColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_N$pressedColor": {"__type__": "cc.Color", "r": 211, "g": 211, "b": 211, "a": 255}, "pressedColor": {"__type__": "cc.Color", "r": 211, "g": 211, "b": 211, "a": 255}, "_N$hoverColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "hoverColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_N$disabledColor": {"__type__": "cc.Color", "r": 124, "g": 124, "b": 124, "a": 255}, "_N$normalSprite": null, "_N$pressedSprite": null, "pressedSprite": null, "_N$hoverSprite": null, "hoverSprite": null, "_N$disabledSprite": null, "_N$target": {"__id__": 23}, "_id": "06WC9SUzBAwYzL3f9HKK0F"}, {"__type__": "cc.Node", "_name": "player", "_objFlags": 0, "_parent": {"__id__": 2}, "_children": [{"__id__": 28}], "_active": true, "_components": [{"__id__": 31}, {"__id__": 32}, {"__id__": 33}, {"__id__": 34}, {"__id__": 35}], "_prefab": null, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 129, "height": 103}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-200, -50, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 2, "groupIndex": 2, "_id": "490sGtQqJO46gfswoOfuO/"}, {"__type__": "cc.Node", "_name": "pet", "_objFlags": 0, "_parent": {"__id__": 27}, "_children": [], "_active": true, "_components": [{"__id__": 29}, {"__id__": 30}], "_prefab": null, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 61, "height": 76}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-79.633, 95.033, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 2, "groupIndex": 2, "_id": "bf9vEAhWZB9ohFXDv0vXEX"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 28}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "25b10bf5-d940-4f06-a6a6-bde8b6d884e2"}, "_type": 0, "_sizeMode": 1, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_atlas": null, "_id": "dfVQ3nLj9Li40EX/JkMDE+"}, {"__type__": "cc.Animation", "_name": "", "_objFlags": 0, "node": {"__id__": 28}, "_enabled": true, "_defaultClip": {"__uuid__": "c694de78-ea1a-4dda-b652-3a39e7b9ef95"}, "_clips": [{"__uuid__": "c694de78-ea1a-4dda-b652-3a39e7b9ef95"}], "playOnLoad": true, "_id": "19HGsGDlRNppaWidK2zsRF"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 27}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "58aa0ddf-527b-4dc7-a004-1bddd943344a"}, "_type": 0, "_sizeMode": 1, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_atlas": null, "_id": "2f4vgQBahL5oncnAlXDaLv"}, {"__type__": "cc.Animation", "_name": "", "_objFlags": 0, "node": {"__id__": 27}, "_enabled": true, "_defaultClip": {"__uuid__": "b0736eb1-3cf1-4105-a374-ec2a36ca1772"}, "_clips": [{"__uuid__": "b0736eb1-3cf1-4105-a374-ec2a36ca1772"}, {"__uuid__": "5dbc00da-012f-4cae-9acd-10a2597b7ddf"}, {"__uuid__": "9d398e4f-aa06-46c0-b5c7-bfd2d2a40cee"}], "playOnLoad": true, "_id": "60drgVM+1AjL7KEUGGeam0"}, {"__type__": "811d289op5IaZQqNSfi7miw", "_name": "", "_objFlags": 0, "node": {"__id__": 27}, "_enabled": true, "jumpForce": 600, "maxFallHeight": 500, "maxJumpTimes": 5, "fixedPositionX": -200, "slideDuration": 1, "slideColliderScale": 0.5, "jumpAnimationDuration": 0.5, "raycastDistance": 15, "_id": "53hOTgrUhKtIRnOK41A/LW"}, {"__type__": "cc.RigidBody", "_name": "", "_objFlags": 0, "node": {"__id__": 27}, "_enabled": true, "_type": 2, "_allowSleep": false, "_gravityScale": 2, "_linearDamping": 0, "_angularDamping": 0, "_linearVelocity": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_angularVelocity": 0, "_fixedRotation": true, "enabledContactListener": true, "bullet": false, "awakeOnLoad": true, "_id": "e37d4HrQdLKrwJmDlWe8+Z"}, {"__type__": "cc.PhysicsBoxCollider", "_name": "", "_objFlags": 0, "node": {"__id__": 27}, "_enabled": true, "tag": 4, "_density": 1, "_sensor": false, "_friction": 0.2, "_restitution": 0, "body": null, "_offset": {"__type__": "cc.Vec2", "x": 60.1, "y": -1.1}, "_size": {"__type__": "cc.Size", "width": 52.1, "height": 98.3}, "_id": "2ddGzVS4xKvoYhM9O4ONNv"}, {"__type__": "cc.<PERSON>", "_name": "", "_objFlags": 0, "node": {"__id__": 2}, "_enabled": true, "_designResolution": {"__type__": "cc.Size", "width": 1111, "height": 500}, "_fitWidth": false, "_fitHeight": true, "_id": "94NoFeBUVClbUrrWXYciNo"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 2}, "_enabled": true, "alignMode": 1, "_target": null, "_alignFlags": 45, "_left": 0, "_right": 0, "_top": 0, "_bottom": 0, "_verticalCenter": 0, "_horizontalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 0, "_id": "aclaH6SLlKx4raL21M2weq"}, {"__type__": "b4aa2PN4s1FdZFP3Z6bsRUb", "_name": "", "_objFlags": 0, "node": {"__id__": 2}, "_enabled": true, "player": {"__id__": 27}, "platformManager": {"__id__": 9}, "coinManager": {"__id__": 11}, "scoreManager": {"__id__": 13}, "backgroundManager": {"__id__": 3}, "startSpeed": 200, "speedIncreaseRate": 10, "maxSpeed": 500, "_id": "6379VVMUdPaIpjEuo329AV"}, {"__type__": "01c11l7ZRlGP4D9QUCH3p/D", "_name": "", "_objFlags": 0, "node": {"__id__": 2}, "_enabled": true, "jumpButton": {"__id__": 21}, "slideButton": {"__id__": 26}, "playerNode": {"__id__": 27}, "_id": "c2HKi3fSNNNodgicfOgHR+"}]